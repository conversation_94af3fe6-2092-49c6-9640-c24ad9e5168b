<template>
    <view class="content">
        <view class="item-01">
            <image 
                src="@/static/products/blood-pressure01.png" 
                class="device-img" 
                mode="aspectFit" 
            />
            <view class="device-desc">
                <Electric-quantity style="text-align: left;" class="iconfont" :ElectricQuantity="10" :isElectric="true"></Electric-quantity>
                <view class="title">远程动态血压监测系统</view>
                <view class="connect"> {{ isConnected ? '已连接' : '未连接' }}</view>
                <view class="tip">不是此台设备</view>
            </view>
            <view class="device-cut" @click="showDeviceList = true">
                <uni-icons custom-prefix="iconfont" type="icon-bangding" size="20" color="white"></uni-icons>
            </view>
        </view>
        <view class="item-02">开始使用</view>


        <!-- 切换设备 -->
        <!-- <u-popup :show="showDeviceList" mode="center"  @close="closeDeviceList" @open="openDeviceList">
            <view>
                <picker-view :indicator-style="indicatorStyle" :value="formData.rjmeasureInterval" @change="bindChangeRJ"
                    class="picker-view">
                    <picker-view-column>
                        <view class="picker-item" v-for="(item, index) in measurementInterval" :key="index">每隔 {{ item.label }} 测量
                        </view>
                    </picker-view-column>
                </picker-view>
            </view>
        </u-popup>
 -->

    </view>
</template>
<script setup lang="ts">
    import ElectricQuantity from '@/components/Electric-quantity.vue';
    import useBlueTooth from "@/common/useBlueTooth";
    import { ref } from 'vue'


    const { state, isAvailable, isConnected, devices, connectToDevice, startDiscovery, pressure_startMeasurement } = useBlueTooth();

    // 选择设备
    const showDeviceList = ref(false)
    

    // function openDeviceList() {
    //     showDeviceList.value = true
    // }

    // function closeDeviceList() {
    //     showDeviceList.value = false
    // }
    // const indicatorStyle = `height: 50px;`

</script>
<style scoped lang="scss">
.content {
    display: flex;
    flex-direction: column;
    height: 720rpx;
    padding: 18rpx;
    overflow: hidden;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4rpx 20rpx  rgba(42, 130, 228, 0.15);
    .item-01 {
        position: relative;
        display: flex;
        flex-direction: row;
        flex: 1;
        padding-left: 36rpx;
        padding-right: 24rpx;
        .device-img {
            width: 280rpx;
            height: 100%;
        }
        .device-desc {
            flex: 1;
            overflow: hidden;
            padding-top: 140rpx;
            padding-left: 20rpx;
            .title {
                font-size: 40rpx;
                padding: 10rpx 0;
            }
            .connect {
                font-size: 40rpx;
                padding-bottom: 10rpx;
            }
            .tip {
                font-size: 28rpx;
                color: #929292;
                text-decoration: underline;
            }
        }
        .device-cut {
            position: absolute;
            right: 10rpx;
            top: 10rpx;
            background: #00DCA7;
            height: 66rpx;
            width: 66rpx;
            line-height: 66rpx;
            text-align: center;
            border-radius: 50%;
        }
    }
    .item-02 {
        height: 128rpx;
        line-height: 128rpx;
        font-size: 52rpx;
        text-align: center;
        color: white;
        opacity: 1;
        border-radius: 24rpx;
        background: rgba(17, 152, 243, 1);
        margin: 38rpx 32rpx;
    }
}
</style>