<template>
    <view class="content">
        <view class="item-01">
            <image 
                src="@/static/products/blood-pressure01.png" 
                class="device-img" 
                mode="aspectFit" 
            />
            <view class="device-desc">
                <view>电池 {{ batteryPercentage }}%</view>
                <view class="title">医用脉搏血氧仪</view>
                <view class="connect" :class="{ connected: isConnected, disconnected: !isConnected }">
                    {{ connectionStatus }}
                </view>
                <view class="tip" v-if="!isConnected">请确认设备已开机，同时手机已打开蓝牙功能</view>
            </view>
            <view class="device-cut" @click="handleDeviceAction">
                <uni-icons 
                    custom-prefix="iconfont" 
                    :type="isConnected ? 'icon-bangding' : 'icon-search'" 
                    size="20" 
                    color="white"
                />
            </view>
        </view>
        <view 
            class="item-02" 
            :class="{ disabled: !isConnected }"
            @click="startMeasurement"
        >
            {{ isConnected ? '开始使用' : '设备未连接' }}
        </view>

        <!-- 设备搜索弹窗 -->
        <u-popup :show="showDeviceList" mode="center" @close="closeDeviceList">
            <view class="device-popup">
                <view class="popup-title">选择设备</view>
                <view class="device-list">
                    <view 
                        v-for="device in availableDevices" 
                        :key="device.deviceId"
                        class="device-item"
                        @click="connectToDevice(device)"
                    >
                        <view class="device-name">{{ device.name }}</view>
                        <view class="device-signal">信号: {{ device.RSSI }}dBm</view>
                    </view>
                    <view v-if="availableDevices.length === 0" class="no-devices">
                        {{ isSearching ? '搜索中...' : '未找到设备' }}
                    </view>
                </view>
                <view class="popup-actions">
                    <button @click="searchDevices" :disabled="isSearching">
                        {{ isSearching ? '搜索中...' : '重新搜索' }}
                    </button>
                    <button @click="closeDeviceList">取消</button>
                </view>
            </view>
        </u-popup>
    </view>
</template>
<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import BluetoothService, { type BluetoothDevice, type BatteryInfo, type MeasurementResult } from '@/common/services/bluetooth-service'

const bluetoothService = BluetoothService.getInstance()

// 响应式数据
const isConnected = ref(false)
const batteryPercentage = ref(0)
const showDeviceList = ref(false)
const availableDevices = ref<BluetoothDevice[]>([])
const isSearching = ref(false)

// 计算属性
const connectionStatus = computed(() => {
    return isConnected.value ? '已连接' : '未连接'
})

// 初始化蓝牙
onMounted(async () => {
    await initBluetooth()
})

// 清理资源
onUnmounted(async () => {
    await bluetoothService.cleanup()
})

// 初始化蓝牙服务
async function initBluetooth() {
    try {
        const success = await bluetoothService.initialize()
        if (success) {
            setupEventListeners()
        }
    } catch (error) {
        uni.showToast({
            title: '蓝牙初始化失败',
            icon: 'error'
        })
    }
}

// 设置事件监听
function setupEventListeners() {
    bluetoothService.setOnConnectionChange((connected) => {
        isConnected.value = connected
        if (connected) {
            uni.showToast({
                title: '设备连接成功',
                icon: 'success'
            })
        } else {
            uni.showToast({
                title: '设备连接断开',
                icon: 'error'
            })
        }
    })

    bluetoothService.setOnBatteryInfo((battery: BatteryInfo) => {
        batteryPercentage.value = battery.percentage
    })

    bluetoothService.setOnError((error) => {
        uni.showToast({
            title: error,
            icon: 'error'
        })
    })

    bluetoothService.setOnMeasurementResult((result: MeasurementResult) => {
        // 处理测量结果，可以跳转到结果页面
        console.log('测量结果:', result)
        uni.navigateTo({
            url: `/pages/bluetooth/result?data=${JSON.stringify(result)}`
        })
    })
}

// 处理设备操作（连接/搜索）
async function handleDeviceAction() {
    if (isConnected.value) {
        // 已连接，显示断开选项或设备信息
        uni.showActionSheet({
            itemList: ['断开设备', '查看设备信息'],
            success: (res) => {
                if (res.tapIndex === 0) {
                    bluetoothService.disconnect()
                }
            }
        })
    } else {
        // 未连接，开始搜索设备
        showDeviceList.value = true
        await searchDevices()
    }
}

// 搜索设备
async function searchDevices() {
    isSearching.value = true
    availableDevices.value = []
    
    try {
        const devices = await bluetoothService.searchDevices(10000)
        console.log(devices)
        availableDevices.value = devices.filter(device => 
            device.name //  && device.name.includes('HYO2') // 过滤相关设备
        )
    } catch (error) {
        uni.showToast({
            title: '搜索设备失败',
            icon: 'error'
        })
    } finally {
        isSearching.value = false
    }
}

// 连接到设备
async function connectToDevice(device: BluetoothDevice) {
    try {

        uni.createBLEConnection({ deviceId: device.deviceId, 
            success: (res) => {
                console.log(res)

                setTimeout(() => {
                    uni.getBLEDeviceServices({ deviceId: device.deviceId,
                        success: (res) => {
                            console.log(res)
                        },
                        fail: (err) => {
                            console.log(err)
                        },
                    })
                }, 3000);

            },
            fail: (err) => {
                console.log(err)
            },
        })
        // console.log(device)
        // const success = await bluetoothService.connectDevice(device.deviceId)
        // if (success) {
        //     closeDeviceList()
        // }
    } catch (error) {
        uni.showToast({
            title: '连接失败',
            icon: 'error'
        })
    }
}

// 开始测量
async function startMeasurement() {
    if (!isConnected.value) {
        uni.showToast({
            title: '请先连接设备',
            icon: 'error'
        })
        return
    }

    try {
        const success = await bluetoothService.startMeasurement()
        if (success) {
            // 跳转到测量页面
            uni.navigateTo({
                url: '/pages/bluetooth/measuring'
            })
        }
    } catch (error) {
        uni.showToast({
            title: '启动测量失败',
            icon: 'error'
        })
    }
}

// 关闭设备列表
function closeDeviceList() {
    showDeviceList.value = false
    availableDevices.value = []
}
</script>
<style scoped lang="scss">
.content {
    display: flex;
    flex-direction: column;
    height: 720rpx;
    padding: 18rpx;
    overflow: hidden;
    border-radius: 16rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4rpx 20rpx rgba(42, 130, 228, 0.15);
    
    .item-01 {
        position: relative;
        display: flex;
        flex-direction: row;
        flex: 1;
        padding-left: 36rpx;
        padding-right: 24rpx;
        
        .device-img {
            width: 280rpx;
            height: 100%;
        }
        
        .device-desc {
            flex: 1;
            overflow: hidden;
            padding-top: 140rpx;
            padding-left: 20rpx;
            
            .title {
                font-size: 40rpx;
                padding: 10rpx 0;
            }
            
            .connect {
                font-size: 40rpx;
                padding-bottom: 10rpx;
                
                &.connected {
                    color: #00DCA7;
                }
                
                &.disconnected {
                    color: #FF6B6B;
                }
            }
            
            .tip {
                color: #929292;
                font-size: 28rpx;
            }
        }
        
        .device-cut {
            position: absolute;
            right: 10rpx;
            top: 10rpx;
            background: #00DCA7;
            height: 66rpx;
            width: 66rpx;
            line-height: 66rpx;
            text-align: center;
            border-radius: 50%;
        }
    }
    
    .item-02 {
        height: 128rpx;
        line-height: 128rpx;
        font-size: 52rpx;
        text-align: center;
        color: white;
        opacity: 1;
        border-radius: 24rpx;
        background: rgba(17, 152, 243, 1);
        margin: 38rpx 32rpx;
        
        &.disabled {
            background: #CCCCCC;
            opacity: 0.6;
        }
    }
}

.device-popup {
    width: 600rpx;
    padding: 40rpx;
    background: white;
    border-radius: 16rpx;
    
    .popup-title {
        font-size: 36rpx;
        font-weight: bold;
        text-align: center;
        margin-bottom: 30rpx;
    }
    
    .device-list {
        max-height: 400rpx;
        overflow-y: auto;
        
        .device-item {
            padding: 20rpx;
            border: 1rpx solid #E5E5E5;
            border-radius: 8rpx;
            margin-bottom: 16rpx;
            
            .device-name {
                font-size: 32rpx;
                margin-bottom: 8rpx;
            }
            
            .device-signal {
                font-size: 24rpx;
                color: #666;
            }
        }
        
        .no-devices {
            text-align: center;
            color: #999;
            padding: 40rpx;
        }
    }
    
    .popup-actions {
        display: flex;
        gap: 20rpx;
        margin-top: 30rpx;
        
        button {
            flex: 1;
            height: 80rpx;
            border-radius: 8rpx;
            border: none;
            font-size: 32rpx;
            
            &:first-child {
                background: #1798F3;
                color: white;
            }
            
            &:last-child {
                background: #F5F5F5;
                color: #333;
            }
        }
    }
}
</style>
