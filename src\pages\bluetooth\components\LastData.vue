<template>
    <view>
        <view class="last-title">最近数据</view>

        <view class="list-box">
            <view v-for="item in dataList" class="p-[30rpx] pb-[40rpx] bg-white mb-[30rpx] text-[32rpx] rounded-[8rpx]">
                <dataItem :item="item"></dataItem>
            </view>

            <view v-if="!dataList.length" class="pt-[100rpx]">
                <up-empty mode="list" text="无测量数据" icon="/static/img/empty1.png" height="100" width="174"></up-empty>
            </view>
        </view>
    </view>
</template>
<script setup lang="ts">
    import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue';

    // import dataItem from '@/pages/home/<USER>/dataItem.vue'

    const dataList = ref([])

</script>
<style scoped lang="scss">
.last-title {
    color: #00DCA7;
    font-size: 48rpx;
    font-weight: 500;
    padding-bottom: 20rpx;
}
.list-box {
    border-radius: 16rpx;
    background: #ffffff;
    min-height: 600rpx;
    box-shadow: 0px 4rpx 20rpx  rgba(42, 130, 228, 0.15);
    margin-bottom: 32rpx;
}
</style>