# 蓝牙测量功能设计文档

## 概述

本设计文档描述了在现有uni-app项目中添加蓝牙血氧测量功能的技术实现方案。该功能将在任务模块顶部添加蓝牙测量入口，支持蓝牙设备连接、实时测量数据展示和历史记录管理。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[任务模块首页] --> B[蓝牙测量入口]
    B --> C[蓝牙连接页面]
    C --> D[测量等待页面]
    D --> E[测量进行页面]
    E --> F[测量结果页面]
    F --> G[历史数据页面]
    
    H[蓝牙服务层] --> I[设备连接管理]
    H --> J[数据接收处理]
    H --> K[状态管理]
    
    L[数据存储层] --> M[本地存储]
    L --> N[状态持久化]
```

### 技术栈选择

- **蓝牙通信**: uni-app蓝牙API (uni.openBluetoothAdapter, uni.createBLEConnection等)
- **状态管理**: Pinia store用于蓝牙连接状态和测量数据管理
- **数据存储**: uni.setStorageSync用于历史数据本地存储
- **UI组件**: uview-plus组件库 + 自定义组件
- **动画效果**: CSS3动画 + uni-app transition组件

## 组件和接口设计

### 页面组件结构

#### 1. 任务模块首页修改 (pages/home/<USER>
- 在NFC区域下方添加蓝牙测量入口
- 入口样式与现有设计保持一致
- 点击跳转到蓝牙连接页面

#### 2. 蓝牙连接页面 (pages/bluetooth/connection.vue)
```vue
<template>
  <view class="bluetooth-connection">
    <!-- 设备状态显示区域 -->
    <view class="device-status">
      <image :src="deviceImage" class="device-image" />
      <view class="device-info">
        <text class="device-name">医用脉搏血氧仪</text>
        <text class="connection-status">{{ connectionStatusText }}</text>
      </view>
    </view>
    
    <!-- 连接提示 -->
    <view class="connection-tips">
      <text>请确认设备已开机，同时手机已打开蓝牙或NFC功能</text>
    </view>
    
    <!-- 开始使用按钮 -->
    <button 
      class="start-button" 
      :disabled="!isConnected"
      @click="startMeasurement"
    >
      开始使用
    </button>
    
    <!-- 历史数据 -->
    <view class="history-section">
      <text class="section-title">最近数据</text>
      <view class="history-list">
        <view 
          v-for="record in historyRecords" 
          :key="record.id"
          class="history-item"
        >
          <text class="spo2-value">{{ record.spo2 }}% SpO₂</text>
          <text class="timestamp">{{ record.timestamp }}</text>
        </view>
      </view>
      <text class="view-more" @click="viewMoreHistory">
        查看更多历史测量数据 >>
      </text>
    </view>
  </view>
</template>
```

#### 3. 测量等待页面 (pages/bluetooth/waiting.vue)
```vue
<template>
  <view class="measurement-waiting">
    <view class="instruction-area">
      <image src="/static/hand-instruction.png" class="instruction-image" />
      <text class="instruction-text">请将血氧探头夹于手指并保持测量</text>
    </view>
    
    <view class="waiting-indicator">
      <view class="pulse-animation"></view>
      <text>等待测量信号...</text>
    </view>
  </view>
</template>
```

#### 4. 测量进行页面 (pages/bluetooth/measuring.vue)
```vue
<template>
  <view class="measurement-progress">
    <!-- 实时数据显示 -->
    <view class="realtime-data">
      <view class="spo2-display">
        <text class="value">{{ currentSpo2 }}</text>
        <text class="unit">SpO₂/%</text>
      </view>
      
      <view class="heart-rate-chart">
        <canvas 
          canvas-id="heartRateChart" 
          class="chart-canvas"
        ></canvas>
      </view>
      
      <view class="pr-display">
        <text class="value">{{ currentPR }}</text>
        <text class="unit">PR/bpm</text>
      </view>
    </view>
    
    <!-- 测量进度 -->
    <view class="progress-indicator">
      <progress :percent="measurementProgress" />
      <text>测量进行中...</text>
    </view>
  </view>
</template>
```

#### 5. 测量结果页面 (pages/bluetooth/result.vue)
```vue
<template>
  <view class="measurement-result">
    <view class="result-display">
      <view class="spo2-result">
        <text class="label">SpO₂:</text>
        <text class="value">{{ finalSpo2 }}%</text>
      </view>
      
      <view class="pr-result">
        <text class="label">PR:</text>
        <text class="value">{{ finalPR }}bpm</text>
      </view>
      
      <view class="timestamp">
        <text>{{ measurementTime }}</text>
      </view>
      
      <view class="health-status">
        <text :class="healthStatusClass">{{ healthStatusText }}</text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="secondary-button" @click="measureAgain">
        再次测量
      </button>
      <button class="primary-button" @click="backToHome">
        返回首页
      </button>
    </view>
  </view>
</template>
```

### 服务层设计

#### 蓝牙服务 (common/services/bluetooth-service.ts)

```typescript
interface BluetoothDevice {
  deviceId: string;
  name: string;
  RSSI: number;
}

interface MeasurementData {
  spo2: number;
  heartRate: number;
  timestamp: number;
  waveform?: number[];
}

class BluetoothService {
  private isConnected: boolean = false;
  private currentDevice: BluetoothDevice | null = null;
  private measurementCallback: ((data: MeasurementData) => void) | null = null;

  // 初始化蓝牙适配器
  async initBluetooth(): Promise<boolean>
  
  // 搜索设备
  async searchDevices(): Promise<BluetoothDevice[]>
  
  // 连接设备
  async connectDevice(deviceId: string): Promise<boolean>
  
  // 断开连接
  async disconnectDevice(): Promise<void>
  
  // 开始接收数据
  startDataReceiving(callback: (data: MeasurementData) => void): void
  
  // 停止接收数据
  stopDataReceiving(): void
  
  // 获取连接状态
  getConnectionStatus(): boolean
}
```

#### 数据管理服务 (common/services/measurement-data-service.ts)

```typescript
interface MeasurementRecord {
  id: string;
  spo2: number;
  heartRate: number;
  timestamp: number;
  duration: number;
  status: 'completed' | 'interrupted';
}

class MeasurementDataService {
  // 保存测量记录
  async saveMeasurementRecord(record: MeasurementRecord): Promise<void>
  
  // 获取历史记录
  async getHistoryRecords(limit?: number): Promise<MeasurementRecord[]>
  
  // 删除记录
  async deleteRecord(id: string): Promise<void>
  
  // 清空所有记录
  async clearAllRecords(): Promise<void>
  
  // 导出数据
  async exportData(): Promise<string>
}
```

### 状态管理设计

#### Pinia Store (stores/bluetooth-measurement.ts)

```typescript
interface BluetoothMeasurementState {
  // 连接状态
  isBluetoothEnabled: boolean;
  isDeviceConnected: boolean;
  currentDevice: BluetoothDevice | null;
  connectionError: string | null;
  
  // 测量状态
  isMeasuring: boolean;
  measurementProgress: number;
  currentMeasurementData: MeasurementData | null;
  
  // 历史数据
  historyRecords: MeasurementRecord[];
  
  // UI状态
  currentPage: 'connection' | 'waiting' | 'measuring' | 'result';
}

export const useBluetoothMeasurementStore = defineStore('bluetoothMeasurement', {
  state: (): BluetoothMeasurementState => ({
    isBluetoothEnabled: false,
    isDeviceConnected: false,
    currentDevice: null,
    connectionError: null,
    isMeasuring: false,
    measurementProgress: 0,
    currentMeasurementData: null,
    historyRecords: [],
    currentPage: 'connection'
  }),
  
  actions: {
    // 初始化蓝牙
    async initializeBluetooth(),
    
    // 连接设备
    async connectToDevice(deviceId: string),
    
    // 开始测量
    async startMeasurement(),
    
    // 停止测量
    async stopMeasurement(),
    
    // 保存测量结果
    async saveMeasurementResult(data: MeasurementData),
    
    // 加载历史记录
    async loadHistoryRecords()
  }
});
```

## 数据模型设计

### 蓝牙设备数据协议

根据医用脉搏血氧仪的标准协议，定义数据解析格式：

```typescript
// 设备数据包结构
interface DeviceDataPacket {
  header: number;      // 数据包头 0xAA
  length: number;      // 数据长度
  command: number;     // 命令类型
  spo2: number;        // 血氧饱和度 (0-100)
  heartRate: number;   // 心率 (30-250 bpm)
  waveform: number[];  // 心率波形数据
  checksum: number;    // 校验和
}

// 数据解析器
class DataParser {
  static parseDeviceData(buffer: ArrayBuffer): MeasurementData | null {
    // 解析蓝牙设备发送的原始数据
    // 返回标准化的测量数据
  }
}
```

### 本地存储数据结构

```typescript
// 本地存储键名
const STORAGE_KEYS = {
  MEASUREMENT_HISTORY: 'bluetooth_measurement_history',
  DEVICE_SETTINGS: 'bluetooth_device_settings',
  USER_PREFERENCES: 'measurement_user_preferences'
};

// 设备设置
interface DeviceSettings {
  lastConnectedDeviceId: string;
  autoConnect: boolean;
  measurementDuration: number; // 秒
  alertThresholds: {
    spo2Min: number;
    spo2Max: number;
    heartRateMin: number;
    heartRateMax: number;
  };
}
```

## 错误处理设计

### 错误类型定义

```typescript
enum BluetoothErrorType {
  BLUETOOTH_NOT_AVAILABLE = 'BLUETOOTH_NOT_AVAILABLE',
  DEVICE_NOT_FOUND = 'DEVICE_NOT_FOUND',
  CONNECTION_FAILED = 'CONNECTION_FAILED',
  CONNECTION_LOST = 'CONNECTION_LOST',
  DATA_PARSING_ERROR = 'DATA_PARSING_ERROR',
  MEASUREMENT_TIMEOUT = 'MEASUREMENT_TIMEOUT',
  PERMISSION_DENIED = 'PERMISSION_DENIED'
}

class BluetoothError extends Error {
  constructor(
    public type: BluetoothErrorType,
    public message: string,
    public recoverable: boolean = true
  ) {
    super(message);
  }
}
```

### 错误处理策略

1. **连接错误**: 自动重试机制，最多重试3次
2. **数据解析错误**: 忽略错误数据包，继续接收
3. **测量超时**: 提示用户重新开始测量
4. **权限错误**: 引导用户到设置页面开启权限

## 测试策略

### 单元测试

- 蓝牙服务类的各个方法测试
- 数据解析器的准确性测试
- 状态管理store的状态变更测试

### 集成测试

- 完整的测量流程测试
- 错误场景的处理测试
- 多设备兼容性测试

### 用户体验测试

- 不同屏幕尺寸的适配测试
- 网络状况变化的处理测试
- 长时间使用的稳定性测试

## 性能优化

### 内存管理

- 及时清理蓝牙连接和监听器
- 限制历史数据的内存占用
- 优化心率波形数据的存储

### 电量优化

- 合理设置蓝牙扫描间隔
- 测量完成后及时断开连接
- 后台运行时降低数据接收频率

### 用户体验优化

- 添加加载状态和进度指示
- 实现平滑的页面切换动画
- 提供清晰的操作反馈