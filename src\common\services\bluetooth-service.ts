interface BluetoothDevice {
  deviceId: string;
  name: string;
  RSSI: number;
  advertisData?: any; // uni-app 返回的广播数据类型
}

interface MeasurementResult {
  systolic: number;      // 收缩压
  diastolic: number;     // 舒张压
  heartRate: number;     // 心率
  heartStatus: number;   // 心跳状态
  errorCode: number;     // 技术报警值
  timestamp: number;     // 时间戳
}

interface BatteryInfo {
  voltage: number;       // 电池电压(mV)
  percentage: number;    // 电量百分比
}

interface CuffPressure {
  pressure: number;      // 袖带压(mmHg)
  timestamp: number;
}

// 数据包类型枚举
enum PacketType {
  START_MEASUREMENT = 0x01,    // 启动测量
  STOP_MEASUREMENT = 0x02,     // 停止测量
  TIME_QUERY = 0x03,           // 查询/设置时间
  MEASUREMENT_RESULT = 0x04,   // 查询/上报测量结果
  CUFF_PRESSURE = 0x01,        // 袖带压(notify)
  BATTERY_INFO = 0x01          // 电池信息(notify)
}

class BluetoothService {
  private static instance: BluetoothService;
  private isInitialized = false;
  private isConnected = false;
  private currentDevice: BluetoothDevice | null = null;
  private deviceId = '';
  private serviceId = '';
  private writeCharacteristicId = '';
  private notifyCharacteristicId = '';
  
  // 事件回调
  private onMeasurementResult?: (result: MeasurementResult) => void;
  private onCuffPressure?: (pressure: CuffPressure) => void;
  private onBatteryInfo?: (battery: BatteryInfo) => void;
  private onConnectionChange?: (connected: boolean) => void;
  private onError?: (error: string) => void;

  private constructor() {}

  static getInstance(): BluetoothService {
    if (!BluetoothService.instance) {
      BluetoothService.instance = new BluetoothService();
    }
    return BluetoothService.instance;
  }

  // 初始化蓝牙
  async initialize(): Promise<boolean> {
    return new Promise((resolve) => {
      uni.openBluetoothAdapter({
        success: (res) => {
          this.isInitialized = true;
          console.log('蓝牙初始化成功', res);
          resolve(true);
        },
        fail: (error) => {
          console.error('蓝牙初始化失败:', error);
          this.onError?.('蓝牙初始化失败，请检查蓝牙是否开启');
          resolve(false);
        }
      });
    });
  }

  // 搜索设备
  async searchDevices(timeout = 10000): Promise<BluetoothDevice[]> {
    if (!this.isInitialized) {
      throw new Error('蓝牙未初始化');
    }

    const devices: BluetoothDevice[] = [];
    
    return new Promise((resolve, reject) => {
      // 开始搜索
      uni.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: false,
        success: () => {
          console.log('开始搜索蓝牙设备');
        },
        fail: (error) => {
          reject(new Error('开始搜索失败: ' + error.errMsg));
        }
      });

      // 监听设备发现
      uni.onBluetoothDeviceFound((res) => {
        res.devices.forEach(device => {
          if (device.name && !devices.find(d => d.deviceId === device.deviceId)) {
            devices.push({
              deviceId: device.deviceId,
              name: device.name,
              RSSI: device.RSSI,
              advertisData: device.advertisData
            });
          }
        });
      });

      // 超时处理
      setTimeout(() => {
        uni.stopBluetoothDevicesDiscovery();
        resolve(devices);
      }, timeout);
    });
  }

  // 连接设备
  async connectDevice(deviceId: string): Promise<boolean> {
    return new Promise((resolve) => {
      // 停止搜索
      uni.stopBluetoothDevicesDiscovery({
        success: () => {
          console.log('停止搜索成功');
        },
        fail: (error) => {
          console.warn('停止搜索失败:', error);
        }
      });

      // 创建连接
      console.log('设备id:', deviceId);
      uni.createBLEConnection({
        deviceId,
        success: (res) => {
          console.log('BLE连接成功', res);
          this.deviceId = deviceId;

          // 获取服务
          uni.getBLEDeviceServices({
            deviceId,
            success: (servicesRes) => {
              console.log('获取服务成功:', servicesRes);
              if (servicesRes.services && servicesRes.services.length > 0) {
                this.serviceId = servicesRes.services[0].uuid; // 假设使用第一个服务

                // 获取特征值
                uni.getBLEDeviceCharacteristics({
                  deviceId,
                  serviceId: this.serviceId,
                  success: (characteristicsRes) => {
                    console.log('获取特征值成功:', characteristicsRes);

                    // 找到写入和通知特征值
                    characteristicsRes.characteristics.forEach(char => {
                      if (char.properties.write) {
                        this.writeCharacteristicId = char.uuid;
                      }
                      if (char.properties.notify) {
                        this.notifyCharacteristicId = char.uuid;
                      }
                    });

                    // 启用通知
                    if (this.notifyCharacteristicId) {
                      uni.notifyBLECharacteristicValueChange({
                        deviceId,
                        serviceId: this.serviceId,
                        characteristicId: this.notifyCharacteristicId,
                        state: true,
                        success: () => {
                          console.log('启用通知成功');
                          this.setupConnectionListeners();
                          this.isConnected = true;
                          console.log('设备连接成功');
                          resolve(true);
                        },
                        fail: (error) => {
                          console.error('启用通知失败:', error);
                          this.onError?.('启用通知失败');
                          resolve(false);
                        }
                      });
                    } else {
                      this.setupConnectionListeners();
                      this.isConnected = true;
                      console.log('设备连接成功');
                      resolve(true);
                    }
                  },
                  fail: (error) => {
                    console.error('获取特征值失败:', error);
                    this.onError?.('获取特征值失败');
                    resolve(false);
                  }
                });
              } else {
                console.error('未找到可用服务');
                this.onError?.('未找到可用服务');
                resolve(false);
              }
            },
            fail: (error) => {
              console.error('获取服务失败:', error);
              this.onError?.('获取服务失败');
              resolve(false);
            }
          });
        },
        fail: (error) => {
          console.error('设备连接失败:', error);
          this.onError?.('设备连接失败: ' + (error.errMsg || '未知错误'));
          resolve(false);
        }
      });
    });
  }

  // 设置连接监听器
  private setupConnectionListeners(): void {
    // 监听数据变化
    uni.onBLECharacteristicValueChange(this.handleDataReceived.bind(this));

    // 监听连接状态变化
    uni.onBLEConnectionStateChange((res) => {
      this.isConnected = res.connected;
      this.onConnectionChange?.(res.connected);
      if (!res.connected) {
        this.currentDevice = null;
        this.deviceId = '';
      }
    });
  }

  // 断开连接
  async disconnect(): Promise<void> {
    if (this.deviceId) {
      return new Promise((resolve) => {
        uni.closeBLEConnection({
          deviceId: this.deviceId,
          success: () => {
            this.isConnected = false;
            this.currentDevice = null;
            this.deviceId = '';
            console.log('设备断开连接');
            resolve();
          },
          fail: (error) => {
            console.error('断开连接失败:', error);
            resolve(); // 即使失败也要resolve，避免阻塞
          }
        });
      });
    }
  }

  // 发送数据包
  private async sendPacket(type: number, data: ArrayBuffer = new ArrayBuffer(0)): Promise<void> {
    if (!this.isConnected || !this.deviceId || !this.writeCharacteristicId) {
      throw new Error('设备未连接');
    }

    return new Promise((resolve, reject) => {
      const dataLength = data.byteLength;
      const packet = new ArrayBuffer(4 + dataLength); // 包头+长度+类型+数据+包尾
      const view = new DataView(packet);

      let offset = 0;
      view.setUint8(offset++, 0x5B); // 包头
      view.setUint8(offset++, dataLength); // 数据长度
      view.setUint8(offset++, type); // 包类型

      // 复制数据
      if (dataLength > 0) {
        const dataView = new DataView(data);
        for (let i = 0; i < dataLength; i++) {
          view.setUint8(offset++, dataView.getUint8(i));
        }
      }

      view.setUint8(offset, 0xB5); // 包尾

      // 将 ArrayBuffer 转换为数组
      const packetArray = Array.from(new Uint8Array(packet));

      uni.writeBLECharacteristicValue({
        deviceId: this.deviceId,
        serviceId: this.serviceId,
        characteristicId: this.writeCharacteristicId,
        value: packetArray,
        success: () => {
          resolve();
        },
        fail: (error) => {
          console.error('发送数据失败:', error);
          reject(error);
        }
      });
    });
  }

  // 启动血压测量
  async startMeasurement(): Promise<boolean> {
    try {
      await this.sendPacket(PacketType.START_MEASUREMENT);
      return true;
    } catch (error) {
      this.onError?.('启动测量失败');
      return false;
    }
  }

  // 停止测量
  async stopMeasurement(): Promise<void> {
    try {
      await this.sendPacket(PacketType.STOP_MEASUREMENT);
    } catch (error) {
      this.onError?.('停止测量失败');
    }
  }

  // 查询时间
  async queryTime(): Promise<void> {
    try {
      await this.sendPacket(PacketType.TIME_QUERY);
    } catch (error) {
      this.onError?.('查询时间失败');
    }
  }

  // 设置时间
  async setTime(timestamp: number): Promise<void> {
    try {
      const data = new ArrayBuffer(4);
      const view = new DataView(data);
      view.setUint32(0, timestamp, true); // 小端序
      await this.sendPacket(PacketType.TIME_QUERY, data);
    } catch (error) {
      this.onError?.('设置时间失败');
    }
  }

  // 查询测量结果
  async queryMeasurementResult(): Promise<void> {
    try {
      await this.sendPacket(PacketType.MEASUREMENT_RESULT);
    } catch (error) {
      this.onError?.('查询测量结果失败');
    }
  }

  // 处理接收到的数据
  private handleDataReceived(res: any): void {
    if (res.deviceId !== this.deviceId) return;

    const data = res.value;
    const view = new DataView(data);
    
    // 验证包头和包尾
    if (view.getUint8(0) !== 0x5B) return;
    
    const length = view.getUint8(1);
    const type = view.getUint8(2);
    
    if (view.getUint8(3 + length) !== 0xB5) return;

    // 根据类型处理数据
    switch (type) {
      case 0x01: // 启动测量回复或袖带压
        if (length === 1) {
          // 启动测量回复
          const result = view.getUint8(3);
          if (result === 0) {
            console.log('测量启动成功');
          } else if (result === 0xFF) {
            this.onError?.('启动失败，设备正忙');
          } else if (result === 0xFE) {
            this.onError?.('启动失败，功能不支持');
          }
        } else if (length === 2) {
          // 袖带压数据
          const pressure = view.getUint16(3, true);
          this.onCuffPressure?.({
            pressure,
            timestamp: Date.now()
          });
        }
        break;
        
      case 0x03: // 时间数据
        if (length === 4) {
          const timestamp = view.getUint32(3, true);
          console.log('设备时间:', new Date(timestamp * 1000));
        }
        break;
        
      case 0x04: // 测量结果
        if (length === 8) {
          const result: MeasurementResult = {
            systolic: view.getUint16(3, true),
            diastolic: view.getUint16(5, true),
            heartRate: view.getUint16(7, true),
            heartStatus: view.getUint8(9),
            errorCode: view.getUint8(10),
            timestamp: Date.now()
          };
          this.onMeasurementResult?.(result);
        }
        break;
        
      case 0x01: // 电池信息 (需要根据实际协议调整)
        if (length === 3) {
          const voltage = view.getUint16(3, true);
          const percentage = view.getUint8(5);
          this.onBatteryInfo?.({
            voltage,
            percentage
          });
        }
        break;
    }
  }

  // 设置事件监听器
  setOnMeasurementResult(callback: (result: MeasurementResult) => void): void {
    this.onMeasurementResult = callback;
  }

  setOnCuffPressure(callback: (pressure: CuffPressure) => void): void {
    this.onCuffPressure = callback;
  }

  setOnBatteryInfo(callback: (battery: BatteryInfo) => void): void {
    this.onBatteryInfo = callback;
  }

  setOnConnectionChange(callback: (connected: boolean) => void): void {
    this.onConnectionChange = callback;
  }

  setOnError(callback: (error: string) => void): void {
    this.onError = callback;
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // 获取当前设备
  getCurrentDevice(): BluetoothDevice | null {
    return this.currentDevice;
  }

  // 清理资源
  async cleanup(): Promise<void> {
    return new Promise(async (resolve) => {
      try {
        if (this.isConnected) {
          await this.disconnect();
        }
        if (this.isInitialized) {
          uni.closeBluetoothAdapter({
            success: () => {
              this.isInitialized = false;
              console.log('蓝牙适配器关闭成功');
              resolve();
            },
            fail: (error) => {
              console.error('关闭蓝牙适配器失败:', error);
              this.isInitialized = false; // 即使失败也要重置状态
              resolve();
            }
          });
        } else {
          resolve();
        }
      } catch (error) {
        console.error('清理蓝牙资源失败:', error);
        resolve();
      }
    });
  }
}

export default BluetoothService;
export type { BluetoothDevice, MeasurementResult, BatteryInfo, CuffPressure };