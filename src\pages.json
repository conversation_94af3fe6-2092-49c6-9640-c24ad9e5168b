{
	"easycom": {
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
		}
	},
	"pages": [{
			"path": "pages/splash",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/login",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/register",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFF"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
                "disableScroll": true,
                "navigationStyle": "custom",
                "navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationStyle": "default",
				"navigationBarBackgroundColor": "#FFF"
			}
		},
		{
			"path": "pages/home/<USER>",
			"style": {
				"app-plus": {
					"titleNView": {
						"titleText": "设置计划"
					}
				},
        "navigationStyle": "custom",
        "navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/privacyAndProtocol",
			"style": {
				"navigationBarTitleText": "title",
				"navigationStyle": "default"
			}
		},
        {
			"path": "pages/bluetooth/index",
			"style": {
                "disableScroll": true,
                "navigationStyle": "custom",
                "navigationBarTextStyle": "black"
			}
		},
		{
			"path": "pages/wifi/index",
			"style": {
				"app-plus": {
					"titleNView": {
						"titleText": "WIFI设置"
					}
				}
			}
		},
		{
			"path": "pages/wifi/addEdit",
			"style": {
				"app-plus": {
					"titleNView": {
						"titleText": "WIFI编辑" // 设置顶部标题显示的文字
					}
				}
			}
		},
		{
			"path": "pages/device/index",
			"style": {
				"app-plus": {
                    "titleNView": {
						"titleText": "设备"
					}
				}
			}
		},
		{
			"path": "pages/personalCentre/index",
			"style": {
				"app-plus": {
					"titleNView": false
				}
			}
		}
	],
	"subPackages": [
        {
			"root": "pageDevice",
			"pages": [{
				"path": "addDevice",
				"style": {
					"navigationBarTitleText": "添加设备"
				}
			}]
		},
        {
			"root": "pageUseInfo",
			"pages": [{
				"path": "useManual",
				"style": {
					"navigationBarTitleText": "用户手册"
				}
			}, {
				"path": "usePolicy",
				"style": {
					"navigationBarTitleText": "隐私条款"
				}
			}
            , {
				"path": "useDeviceList",
				"style": {
					"navigationBarTitleText": "设备管理"
				}
			}
            , {
				"path": "setList",
				"style": {
					"navigationBarTitleText": "通用设置"
				}
			}
            , {
				"path": "about",
				"style": {
					"navigationBarTitleText": "关于我们"
				}
			}
            , {
				"path": "historicalArchives",
				"style": {
					"navigationBarTitleText": "历史档案"
				}
			}
            , {
				"path": "appdicom",
				"style": {
					"navigationBarTitleText": "图像预览"
				}
			}
            ]
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "24小时动态血压",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#9d9d9d",
		"selectedColor": "#414141",
		"backgroundColor": "",
		"position": "bottom",
		"borderStyle": "white",
		"list": [{
				"pagePath": "pages/home/<USER>",
				"text": "任务",
				"iconPath": "static/navigator/Home.png",
				"selectedIconPath": "static/navigator/Home_HL.png"
			},
			{
				"pagePath": "pages/wifi/index",
				"text": "家庭WIFI设置",
				"iconPath": "static/navigator/wifi.png",
				"selectedIconPath": "static/navigator/wifi_HL.png"
			},
			{
				"pagePath": "pages/personalCentre/index",
				"text": "我的",
				"iconPath": "static/navigator/PersonalCentre.png",
				"selectedIconPath": "static/navigator/PersonalCentre_HL.png"
			}
		]
	}
}