<template>
    <view class="h-full flex flex-col box-border">
        <up-navbar
            :autoBack="true"
            :placeholder="true"
            bgColor="#fff"
            height="82rpx"
            >
            <template #right>
                蓝牙已连接
            </template>
        </up-navbar>

        <view class="p-[30rpx] pb-[32rpx]">
            <BlueToothDevice></BlueToothDevice>
        </view>
        <view class="px-[32rpx] pt-[20rpx]">
            <LastData></LastData>
        </view>
    </view>
</template>
<script setup lang="ts">

import BlueToothDevice from './components/BlueToothDevice.vue'
import LastData from './components/LastData.vue'

</script>
<style scoped lang="scss">
page {
    background: #F5F5F5;
}

</style>