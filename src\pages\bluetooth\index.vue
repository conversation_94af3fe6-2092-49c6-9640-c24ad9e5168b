<template>
    <view class="h-full flex flex-col box-border">
        <up-navbar
            :autoBack="true"
            :placeholder="true"
            bgColor="#fff"
            height="82rpx"
            >
            <template #right>
                <u-loading-icon v-if="isBluetoothDiscovering" size="20" style="margin-right: 10rpx;" width="42rpx" height="20rpx"></u-loading-icon>
                <text v-if="isBluetoothConnected && isBluetoothAvailable" class="blue-on">蓝牙已连接</text>
                <text v-else-if="isBluetoothAvailable" style="color: #929292;">手机蓝牙已打开</text>
                <text v-else style="color: #929292; font-size: 28rpx;">请确认设备已开机，同时手机已打开蓝牙</text>
            </template>
        </up-navbar>

        <view class="p-[30rpx] pb-[32rpx]">
            <BlueToothDevice ref="bluetoothDeviceRef"></BlueToothDevice>
        </view>
        <view class="px-[32rpx] pt-[20rpx]">
            <!-- <LastData></LastData> -->
        </view>
    </view>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import BlueToothDevice from './components/BlueToothDevice.vue'
import LastData from './components/LastData.vue'

// 获取子组件的引用
const bluetoothDeviceRef = ref()

// 计算属性来访问子组件的状态
const isBluetoothAvailable = computed(() => {
    return bluetoothDeviceRef.value?.state.isAvailable || false
})

const isBluetoothDiscovering = computed(() => {
    return bluetoothDeviceRef.value?.state.isDiscovering || false
})

const isBluetoothConnected = computed(() => {
    return bluetoothDeviceRef.value?.state.isConnected || false
})

</script>
<style scoped lang="scss">
page {
    background: #F5F5F5;
}
.blue-on {
    color: #0045FF;
    font-size: 32rpx;
    font-weight: bold;
}
</style>