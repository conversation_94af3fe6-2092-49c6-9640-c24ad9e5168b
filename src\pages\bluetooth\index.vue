<template>
    <view class="h-full flex flex-col box-border">
        <up-navbar
            :autoBack="true"
            :placeholder="true"
            bgColor="#fff"
            height="82rpx"
            >
            <template #right>
                <u-loading-icon v-if="isBluetoothDiscovering" size="20" style="margin-right: 10rpx;"></u-loading-icon>
                <text v-if="isBluetoothAvailable" class="blue-on">蓝牙已开启</text>
                <text v-else>蓝牙未开启，请开启手机蓝牙</text>
            </template>
        </up-navbar>

        <view class="p-[30rpx] pb-[32rpx]">
            <BlueToothDevice ref="bluetoothDeviceRef"></BlueToothDevice>
        </view>
        <view class="px-[32rpx] pt-[20rpx]">
            <LastData></LastData>
        </view>
    </view>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import BlueToothDevice from './components/BlueToothDevice.vue'
import LastData from './components/LastData.vue'

// 获取子组件的引用
const bluetoothDeviceRef = ref()

// 计算属性来访问子组件的状态
const isBluetoothAvailable = computed(() => {
    return bluetoothDeviceRef.value?.state.isAvailable || false
})

const isBluetoothDiscovering = computed(() => {
    return bluetoothDeviceRef.value?.state.isDiscovering || false
})
</script>
<style scoped lang="scss">
page {
    background: #F5F5F5;
}
.blue-on {
    color: #0045FF;
    font-size: 32rpx;
    font-weight: bold;
}
</style>