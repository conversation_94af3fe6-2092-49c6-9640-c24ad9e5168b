# 项目开发规范

## 代码风格规范

### 命名规范
- **文件命名**: 使用kebab-case格式，如 `user-profile.vue`
- **组件命名**: 使用PascalCase格式，如 `UserProfile`
- **变量命名**: 使用camelCase格式，如 `userName`
- **常量命名**: 使用UPPER_SNAKE_CASE格式，如 `API_BASE_URL`

### Vue组件规范
- 组件文件必须使用`.vue`扩展名
- 组件内部结构顺序：`<template>` → `<script>` → `<style>`
- 使用Composition API（`<script setup>`）作为首选方式
- 组件props必须定义类型和默认值

### TypeScript规范
- 所有新文件必须使用TypeScript
- 接口命名使用`I`前缀，如 `IUserInfo`
- 类型定义放在`src/types`目录下
- 避免使用`any`类型，优先使用具体类型定义

## 目录结构规范

### 页面组织
- 页面文件放在`src/pages`目录下
- 每个页面模块创建独立文件夹
- 页面相关组件放在对应页面文件夹的`components`子目录

### 组件组织
- 公共组件放在`src/components`目录
- 业务组件按功能模块分类
- 每个组件文件夹包含：组件文件、样式文件、类型定义

### 工具函数
- 工具函数放在`src/common/utils`目录
- 按功能分类创建不同的工具文件
- 每个工具函数必须有JSDoc注释

## 代码质量要求

### 注释规范
- 所有公共函数必须有JSDoc注释
- 复杂业务逻辑必须添加中文注释
- 组件props和emits必须有注释说明

### 错误处理
- 所有API调用必须包含错误处理
- 使用统一的错误处理机制
- 用户友好的错误提示信息

### 性能优化
- 合理使用`v-if`和`v-show`
- 大列表使用虚拟滚动
- 图片懒加载和压缩优化