# uni-app 开发指南

## 平台兼容性

### 跨平台开发原则
- 优先使用uni-app官方组件和API
- 避免使用平台特定的原生代码
- 条件编译时明确标注平台差异

### 条件编译规范
```javascript
// #ifdef H5
// H5平台特定代码
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特定代码
// #endif

// #ifdef APP-PLUS
// App平台特定代码
// #endif
```

## 组件使用规范

### 内置组件
- 优先使用uni-app内置组件
- 自定义组件需考虑多端兼容性
- 组件属性使用驼峰命名法

### 第三方组件库
- 当前项目使用uview-plus组件库
- 引入新组件前需评估包体积影响
- 确保组件在目标平台正常工作

## 页面配置

### pages.json配置
- 页面路径使用相对路径
- 页面标题使用中文
- 合理配置页面样式和导航栏

### 路由管理
- 使用uni.navigateTo进行页面跳转
- 参数传递使用encodeURIComponent编码
- 返回操作使用uni.navigateBack

## 样式开发

### 单位使用
- 优先使用rpx单位适配不同屏幕
- 边框使用px单位保持一致性
- 字体大小根据设计稿使用合适单位

### 样式组织
- 全局样式定义在uni.scss
- 组件样式使用scoped
- 公共样式类定义在common目录

## API调用规范

### 网络请求
- 使用uni.request进行网络请求
- 统一封装请求方法
- 处理不同平台的网络状态

### 本地存储
- 使用uni.setStorageSync/uni.getStorageSync
- 敏感数据需要加密存储
- 定期清理无用的存储数据

## 调试和测试

### 开发调试
- 使用HBuilderX内置调试工具
- 真机调试验证功能完整性
- 多端测试确保兼容性

### 性能监控
- 监控页面加载时间
- 检查内存使用情况
- 优化资源加载策略